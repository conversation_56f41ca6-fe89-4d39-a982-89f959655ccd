package model

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPasswordValidateRequest(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		expected PasswordValidateRequest
		hasError bool
	}{
		{
			name:     "valid request",
			jsonData: `{"password": "TestPassword123!"}`,
			expected: PasswordValidateRequest{Password: "TestPassword123!"},
			hasError: false,
		},
		{
			name:     "empty password",
			jsonData: `{"password": ""}`,
			expected: PasswordValidateRequest{Password: ""},
			hasError: false,
		},
		{
			name:     "missing password field",
			jsonData: `{}`,
			expected: PasswordValidateRequest{Password: ""},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req PasswordValidateRequest
			err := json.Unmarshal([]byte(tt.jsonData), &req)
			
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, req)
			}
		})
	}
}

func TestPasswordValidateResponse(t *testing.T) {
	response := PasswordValidateResponse{
		Valid:   true,
		Strong:  false,
		Errors:  []string{"password should contain special characters"},
		Message: "Password is valid but could be stronger",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(response)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), `"valid":true`)
	assert.Contains(t, string(jsonData), `"strong":false`)

	// Test JSON unmarshaling
	var unmarshaled PasswordValidateResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, response, unmarshaled)
}

func TestPasswordResetRequest(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		expected PasswordResetRequest
		hasError bool
	}{
		{
			name:     "valid email",
			jsonData: `{"email": "<EMAIL>"}`,
			expected: PasswordResetRequest{Email: "<EMAIL>"},
			hasError: false,
		},
		{
			name:     "empty email",
			jsonData: `{"email": ""}`,
			expected: PasswordResetRequest{Email: ""},
			hasError: false,
		},
		{
			name:     "missing email field",
			jsonData: `{}`,
			expected: PasswordResetRequest{Email: ""},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req PasswordResetRequest
			err := json.Unmarshal([]byte(tt.jsonData), &req)
			
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, req)
			}
		})
	}
}

func TestPasswordResetResponse(t *testing.T) {
	response := PasswordResetResponse{
		Success: true,
		Message: "Password reset email sent successfully",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(response)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), `"success":true`)
	assert.Contains(t, string(jsonData), `"message":"Password reset email sent successfully"`)

	// Test JSON unmarshaling
	var unmarshaled PasswordResetResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, response, unmarshaled)
}

func TestPasswordResetConfirmRequest(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		expected PasswordResetConfirmRequest
		hasError bool
	}{
		{
			name:     "valid request",
			jsonData: `{"token": "abc123", "new_password": "NewPassword123!"}`,
			expected: PasswordResetConfirmRequest{Token: "abc123", NewPassword: "NewPassword123!"},
			hasError: false,
		},
		{
			name:     "empty fields",
			jsonData: `{"token": "", "new_password": ""}`,
			expected: PasswordResetConfirmRequest{Token: "", NewPassword: ""},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req PasswordResetConfirmRequest
			err := json.Unmarshal([]byte(tt.jsonData), &req)
			
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, req)
			}
		})
	}
}

func TestPasswordChangeRequest(t *testing.T) {
	tests := []struct {
		name     string
		jsonData string
		expected PasswordChangeRequest
		hasError bool
	}{
		{
			name:     "valid request",
			jsonData: `{"current_password": "OldPassword123!", "new_password": "NewPassword123!"}`,
			expected: PasswordChangeRequest{CurrentPassword: "OldPassword123!", NewPassword: "NewPassword123!"},
			hasError: false,
		},
		{
			name:     "empty fields",
			jsonData: `{"current_password": "", "new_password": ""}`,
			expected: PasswordChangeRequest{CurrentPassword: "", NewPassword: ""},
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req PasswordChangeRequest
			err := json.Unmarshal([]byte(tt.jsonData), &req)
			
			if tt.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, req)
			}
		})
	}
}

func TestPasswordChangeResponse(t *testing.T) {
	response := PasswordChangeResponse{
		Success: true,
		Message: "Password changed successfully",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(response)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), `"success":true`)
	assert.Contains(t, string(jsonData), `"message":"Password changed successfully"`)

	// Test JSON unmarshaling
	var unmarshaled PasswordChangeResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, response, unmarshaled)
}

func TestValidationError(t *testing.T) {
	error := ValidationError{
		Field:   "password",
		Message: "password is required",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(error)
	assert.NoError(t, err)
	assert.Contains(t, string(jsonData), `"field":"password"`)
	assert.Contains(t, string(jsonData), `"message":"password is required"`)

	// Test JSON unmarshaling
	var unmarshaled ValidationError
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, error, unmarshaled)
}

func TestPasswordValidateResponseWithErrors(t *testing.T) {
	response := PasswordValidateResponse{
		Valid:  false,
		Strong: false,
		Errors: []string{
			"password must be at least 8 characters long",
			"password must contain at least one uppercase letter",
		},
		Message: "Password is invalid",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(response)
	assert.NoError(t, err)
	
	// Test that errors array is properly serialized
	assert.Contains(t, string(jsonData), `"errors":[`)
	assert.Contains(t, string(jsonData), `"password must be at least 8 characters long"`)
	assert.Contains(t, string(jsonData), `"password must contain at least one uppercase letter"`)

	// Test JSON unmarshaling
	var unmarshaled PasswordValidateResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, response, unmarshaled)
	assert.Len(t, unmarshaled.Errors, 2)
}

func TestPasswordValidateResponseWithoutErrors(t *testing.T) {
	response := PasswordValidateResponse{
		Valid:   true,
		Strong:  true,
		Errors:  nil,
		Message: "Password is valid and strong",
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(response)
	assert.NoError(t, err)
	
	// Test that errors field is omitted when nil (due to omitempty tag)
	assert.NotContains(t, string(jsonData), `"errors"`)

	// Test JSON unmarshaling
	var unmarshaled PasswordValidateResponse
	err = json.Unmarshal(jsonData, &unmarshaled)
	assert.NoError(t, err)
	assert.Equal(t, response.Valid, unmarshaled.Valid)
	assert.Equal(t, response.Strong, unmarshaled.Strong)
	assert.Equal(t, response.Message, unmarshaled.Message)
	assert.Nil(t, unmarshaled.Errors)
}
