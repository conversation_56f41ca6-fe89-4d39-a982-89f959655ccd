package api

import (
	"testing"

	"github.com/homewizeAI/webexpms/model"
	"github.com/stretchr/testify/assert"
)

func TestValidatePasswordRequest(t *testing.T) {
	tests := []struct {
		name     string
		req      model.PasswordValidateRequest
		expected int // number of validation errors
	}{
		{
			name:     "valid password",
			req:      model.PasswordValidateRequest{Password: "ValidPassword123!"},
			expected: 0,
		},
		{
			name:     "empty password",
			req:      model.PasswordValidateRequest{Password: ""},
			expected: 1,
		},
		{
			name:     "password too short",
			req:      model.PasswordValidateRequest{Password: "short"},
			expected: 1,
		},
		{
			name:     "password too long",
			req:      model.PasswordValidateRequest{Password: string(make([]byte, 129))},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidatePasswordRequest(tt.req)
			assert.Equal(t, tt.expected, len(errors))
		})
	}
}

func TestValidatePasswordStrength(t *testing.T) {
	tests := []struct {
		name     string
		password string
		valid    bool
		strong   bool
	}{
		{
			name:     "strong password",
			password: "StrongPassword123!",
			valid:    true,
			strong:   true,
		},
		{
			name:     "valid but weak password - no special char",
			password: "ValidPassword123",
			valid:    true,
			strong:   true, // Still strong according to our basic rules
		},
		{
			name:     "weak password - no uppercase",
			password: "weakpassword123!",
			valid:    true,
			strong:   false,
		},
		{
			name:     "weak password - no lowercase",
			password: "WEAKPASSWORD123!",
			valid:    true,
			strong:   false,
		},
		{
			name:     "weak password - no digit",
			password: "WeakPassword!",
			valid:    true,
			strong:   false,
		},
		{
			name:     "invalid password - too short",
			password: "Short1!",
			valid:    false,
			strong:   false,
		},
		{
			name:     "weak password - common pattern",
			password: "Password123!",
			valid:    true,
			strong:   false,
		},
		{
			name:     "weak password - sequential chars",
			password: "Abc123456!",
			valid:    true,
			strong:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidatePasswordStrength(tt.password)
			assert.Equal(t, tt.valid, result.Valid, "Valid flag mismatch")
			assert.Equal(t, tt.strong, result.Strong, "Strong flag mismatch")
			
			if !tt.valid {
				assert.NotEmpty(t, result.Errors, "Should have errors for invalid password")
			}
		})
	}
}

func TestValidatePasswordResetRequest(t *testing.T) {
	tests := []struct {
		name     string
		req      model.PasswordResetRequest
		expected int // number of validation errors
	}{
		{
			name:     "valid email",
			req:      model.PasswordResetRequest{Email: "<EMAIL>"},
			expected: 0,
		},
		{
			name:     "empty email",
			req:      model.PasswordResetRequest{Email: ""},
			expected: 1,
		},
		{
			name:     "invalid email format",
			req:      model.PasswordResetRequest{Email: "invalid-email"},
			expected: 1,
		},
		{
			name:     "invalid email format - no domain",
			req:      model.PasswordResetRequest{Email: "test@"},
			expected: 1,
		},
		{
			name:     "invalid email format - no @",
			req:      model.PasswordResetRequest{Email: "testexample.com"},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidatePasswordResetRequest(tt.req)
			assert.Equal(t, tt.expected, len(errors))
		})
	}
}

func TestHasUppercase(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"HasUppercase", true},
		{"nouppercase", false},
		{"ALLUPPERCASE", true},
		{"123!@#", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasUppercase(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasLowercase(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"HasLowercase", true},
		{"NOLOWERCASE", false},
		{"alllowercase", true},
		{"123!@#", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasLowercase(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasDigit(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"HasDigit123", true},
		{"NoDigit", false},
		{"123456", true},
		{"!@#$%", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasDigit(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasSpecialChar(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"HasSpecial!", true},
		{"NoSpecial", false},
		{"!@#$%^&*()", true},
		{"123abc", false},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasSpecialChar(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasCommonPatterns(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"password123", true},
		{"MyPassword123", true},
		{"qwerty456", true},
		{"UniquePassword789", false},
		{"abc123def", true},
		{"ComplexP@ssw0rd", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasCommonPatterns(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHasSequentialChars(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"abc123", true},
		{"xyz789", true},
		{"cba321", true},
		{"random123", false},
		{"ab", false}, // too short
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasSequentialChars(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestFormatValidationErrors(t *testing.T) {
	tests := []struct {
		name     string
		errors   []model.ValidationError
		expected string
	}{
		{
			name:     "no errors",
			errors:   []model.ValidationError{},
			expected: "",
		},
		{
			name: "single error",
			errors: []model.ValidationError{
				{Field: "password", Message: "password is required"},
			},
			expected: "password: password is required",
		},
		{
			name: "multiple errors",
			errors: []model.ValidationError{
				{Field: "password", Message: "password is required"},
				{Field: "email", Message: "email is invalid"},
			},
			expected: "password: password is required; email: email is invalid",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatValidationErrors(tt.errors)
			assert.Equal(t, tt.expected, result)
		})
	}
}
