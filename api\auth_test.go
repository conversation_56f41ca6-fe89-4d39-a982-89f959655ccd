package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/webexpms/model"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// Auth routes group
	authGroup := r.Group("/auth")
	{
		authGroup.POST("/pwd-validate", PasswordValidateHandler)
		authGroup.POST("/pwd-reset", PasswordResetHandler)
		authGroup.POST("/pwd-reset-confirm", PasswordResetConfirmHandler)
		authGroup.POST("/pwd-change", PasswordChangeHandler)
		authGroup.POST("/pwd-strength", PasswordStrengthCheckHandler)
	}

	return r
}

func TestPasswordValidateHandler(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "valid strong password",
			requestBody: model.PasswordValidateRequest{
				Password: "StrongPassword123!",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordValidateResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response.Valid)
				assert.True(t, response.Strong)
			},
		},
		{
			name: "valid but weak password",
			requestBody: model.PasswordValidateRequest{
				Password: "weakpassword",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordValidateResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response.Valid)
				assert.False(t, response.Strong)
				assert.NotEmpty(t, response.Errors)
			},
		},
		{
			name: "invalid password - too short",
			requestBody: model.PasswordValidateRequest{
				Password: "short",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordValidateResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.False(t, response.Valid)
				assert.False(t, response.Strong)
				assert.NotEmpty(t, response.Errors)
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return bad request error
			},
		},
		{
			name: "empty password",
			requestBody: model.PasswordValidateRequest{
				Password: "",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return validation error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var reqBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				reqBody = []byte(str)
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/pwd-validate", bytes.NewBuffer(reqBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.checkResponse != nil {
				tt.checkResponse(t, w.Body.Bytes())
			}
		})
	}
}

func TestPasswordResetHandler(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "valid email",
			requestBody: model.PasswordResetRequest{
				Email: "<EMAIL>",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordResetResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.Contains(t, response.Message, "successfully")
			},
		},
		{
			name: "invalid email",
			requestBody: model.PasswordResetRequest{
				Email: "invalid-email",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return validation error
			},
		},
		{
			name: "empty email",
			requestBody: model.PasswordResetRequest{
				Email: "",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return validation error
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return bad request error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var reqBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				reqBody = []byte(str)
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/pwd-reset", bytes.NewBuffer(reqBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.checkResponse != nil {
				tt.checkResponse(t, w.Body.Bytes())
			}
		})
	}
}

func TestPasswordResetConfirmHandler(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "valid request",
			requestBody: model.PasswordResetConfirmRequest{
				Token:       "valid-token-123",
				NewPassword: "NewStrongPassword123!",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordResetConfirmResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
			},
		},
		{
			name: "weak new password",
			requestBody: model.PasswordResetConfirmRequest{
				Token:       "valid-token-123",
				NewPassword: "weak",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return validation error for weak password
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return bad request error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var reqBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				reqBody = []byte(str)
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/pwd-reset-confirm", bytes.NewBuffer(reqBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.checkResponse != nil {
				tt.checkResponse(t, w.Body.Bytes())
			}
		})
	}
}

func TestPasswordChangeHandler(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "valid request",
			requestBody: model.PasswordChangeRequest{
				CurrentPassword: "OldPassword123!",
				NewPassword:     "NewStrongPassword123!",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response model.PasswordChangeResponse
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
			},
		},
		{
			name: "weak new password",
			requestBody: model.PasswordChangeRequest{
				CurrentPassword: "OldPassword123!",
				NewPassword:     "weak",
			},
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return validation error for weak password
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return bad request error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var reqBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				reqBody = []byte(str)
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/pwd-change", bytes.NewBuffer(reqBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.checkResponse != nil {
				tt.checkResponse(t, w.Body.Bytes())
			}
		})
	}
}

func TestPasswordStrengthCheckHandler(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		checkResponse  func(t *testing.T, body []byte)
	}{
		{
			name: "strong password",
			requestBody: model.PasswordValidateRequest{
				Password: "StrongPassword123!",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response bool
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.True(t, response)
			},
		},
		{
			name: "weak password",
			requestBody: model.PasswordValidateRequest{
				Password: "weakpassword",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response bool
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.False(t, response)
			},
		},
		{
			name: "invalid password - too short",
			requestBody: model.PasswordValidateRequest{
				Password: "short",
			},
			expectedStatus: http.StatusOK,
			checkResponse: func(t *testing.T, body []byte) {
				var response bool
				err := json.Unmarshal(body, &response)
				assert.NoError(t, err)
				assert.False(t, response)
			},
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			checkResponse: func(t *testing.T, body []byte) {
				// Should return bad request error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var reqBody []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				reqBody = []byte(str)
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req, err := http.NewRequest("POST", "/auth/pwd-strength", bytes.NewBuffer(reqBody))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			if tt.checkResponse != nil {
				tt.checkResponse(t, w.Body.Bytes())
			}
		})
	}
}
