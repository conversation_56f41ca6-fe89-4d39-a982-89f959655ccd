package api

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/homewizeAI/webexpms/model"
)

var (
	// Email validation regex
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
)

// ValidatePasswordRequest validates the password validation request
func ValidatePasswordRequest(req model.PasswordValidateRequest) []model.ValidationError {
	var errors []model.ValidationError

	// Validate password
	if req.Password == "" {
		errors = append(errors, model.ValidationError{
			Field:   "password",
			Message: "password is required",
		})
	} else {
		if len(req.Password) < 8 {
			errors = append(errors, model.ValidationError{
				Field:   "password",
				Message: "password must be at least 8 characters long",
			})
		}
		if len(req.Password) > 128 {
			errors = append(errors, model.ValidationError{
				Field:   "password",
				Message: "password must be no more than 128 characters long",
			})
		}
	}

	return errors
}

// ValidatePasswordStrength validates password strength and returns detailed feedback
func ValidatePasswordStrength(password string) model.PasswordValidateResponse {
	var errors []string
	valid := true
	strong := true

	// Check minimum length
	if len(password) < 8 {
		errors = append(errors, "password must be at least 8 characters long")
		valid = false
		strong = false
	}

	// Check maximum length
	if len(password) > 128 {
		errors = append(errors, "password must be no more than 128 characters long")
		valid = false
		strong = false
	}

	// Check for at least one uppercase letter
	if !hasUppercase(password) {
		errors = append(errors, "password must contain at least one uppercase letter")
		strong = false
	}

	// Check for at least one lowercase letter
	if !hasLowercase(password) {
		errors = append(errors, "password must contain at least one lowercase letter")
		strong = false
	}

	// Check for at least one digit
	if !hasDigit(password) {
		errors = append(errors, "password must contain at least one digit")
		strong = false
	}

	// Check for at least one special character
	if !hasSpecialChar(password) {
		errors = append(errors, "password should contain at least one special character")
		// Note: This doesn't make it invalid, just not strong
	}

	// Check for common patterns
	if hasCommonPatterns(password) {
		errors = append(errors, "password contains common patterns that make it weak")
		strong = false
	}

	message := "Password is valid and strong"
	if !valid {
		message = "Password is invalid"
	} else if !strong {
		message = "Password is valid but could be stronger"
	}

	return model.PasswordValidateResponse{
		Valid:   valid,
		Strong:  strong,
		Errors:  errors,
		Message: message,
	}
}

// ValidatePasswordResetRequest validates the password reset request
func ValidatePasswordResetRequest(req model.PasswordResetRequest) []model.ValidationError {
	var errors []model.ValidationError

	// Validate email
	if req.Email == "" {
		errors = append(errors, model.ValidationError{
			Field:   "email",
			Message: "email is required",
		})
	} else {
		if !emailRegex.MatchString(req.Email) {
			errors = append(errors, model.ValidationError{
				Field:   "email",
				Message: "invalid email format",
			})
		}
	}

	return errors
}

// Helper functions for password validation
func hasUppercase(s string) bool {
	for _, char := range s {
		if char >= 'A' && char <= 'Z' {
			return true
		}
	}
	return false
}

func hasLowercase(s string) bool {
	for _, char := range s {
		if char >= 'a' && char <= 'z' {
			return true
		}
	}
	return false
}

func hasDigit(s string) bool {
	for _, char := range s {
		if char >= '0' && char <= '9' {
			return true
		}
	}
	return false
}

func hasSpecialChar(s string) bool {
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	for _, char := range s {
		if strings.ContainsRune(specialChars, char) {
			return true
		}
	}
	return false
}

func hasCommonPatterns(password string) bool {
	lower := strings.ToLower(password)
	
	// Check for common weak patterns
	commonPatterns := []string{
		"password", "123456", "qwerty", "abc123", "admin", "user",
		"login", "welcome", "letmein", "monkey", "dragon",
	}
	
	for _, pattern := range commonPatterns {
		if strings.Contains(lower, pattern) {
			return true
		}
	}
	
	// Check for sequential characters
	if hasSequentialChars(password) {
		return true
	}
	
	return false
}

func hasSequentialChars(password string) bool {
	if len(password) < 3 {
		return false
	}
	
	for i := 0; i < len(password)-2; i++ {
		// Check for ascending sequence
		if password[i+1] == password[i]+1 && password[i+2] == password[i]+2 {
			return true
		}
		// Check for descending sequence
		if password[i+1] == password[i]-1 && password[i+2] == password[i]-2 {
			return true
		}
	}
	
	return false
}

// FormatValidationErrors formats validation errors into a readable string
func FormatValidationErrors(errors []model.ValidationError) string {
	if len(errors) == 0 {
		return ""
	}

	var messages []string
	for _, err := range errors {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}

	return strings.Join(messages, "; ")
}
