package model

// PasswordValidateRequest represents the request payload for password validation
type PasswordValidateRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// PasswordValidateResponse represents the response for password validation
type PasswordValidateResponse struct {
	Valid   bool     `json:"valid"`
	Strong  bool     `json:"strong"`
	Errors  []string `json:"errors,omitempty"`
	Message string   `json:"message,omitempty"`
}

// PasswordResetRequest represents the request payload for password reset
type PasswordResetRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type PasswordResetUpdateRequest struct {
	ResetKey    string `json:"reset_key" binding:"required"`
	ResetID     uint64 `json:"reset_id" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// PasswordResetResponse represents the response for password reset request
type PasswordResetResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// PasswordResetConfirmRequest represents the request payload for confirming password reset
type PasswordResetConfirmRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// PasswordResetConfirmResponse represents the response for password reset confirmation
type PasswordResetConfirmResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// PasswordChangeRequest represents the request payload for changing password
type PasswordChangeRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// PasswordChangeResponse represents the response for password change
type PasswordChangeResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}
