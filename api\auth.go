package api

import (
	"fmt"
	"log"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	accmgtmodel "github.com/homewizeAI/accmgtms/model"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apitypes"
	authmodel "github.com/homewizeAI/authms/model"

	"github.com/homewizeAI/errs"
	"github.com/homewizeAI/utils"
	"github.com/homewizeAI/webexpms/cfg"
	"github.com/homewizeAI/webexpms/core"
	"github.com/homewizeAI/webexpms/model"
)

func AuthPwdValidate(c *gin.Context) {
	var data model.PasswordValidateRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	subdomain := utils.SubdomainGet(c.Request.Host)

	ctx := c.Request.Context()
	usr, err := cfg.AccmgtClient.UserGetByEmailAndShortName(ctx, data.Email, subdomain)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespUnauthorized(c, "invalid email/pwd")
			return
		} else if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return

		}
		apicommon.RespNotFound(c, "user")
		return
	}

	tkn, err := cfg.AuthClient.PwdValidate(ctx, authmodel.PwdValidateRequest{
		UsrID:    usr.ID,
		Password: data.Password,
	})
	if err != nil {
		if errs.IsUnauthorized(err) {
			apicommon.RespUnauthorized(c, "invalid email/pwd")
			return
		} else if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		} else if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "user")
		return
	}
	apicommon.RespOk(c, tkn)
}

func AuthPwdResetReq(c *gin.Context) {
	var data model.PasswordResetRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}
	subdomain := utils.SubdomainGet(c.Request.Host)
	ctx := c.Request.Context()
	usr, err := cfg.AccmgtClient.UserGetByEmailAndShortName(ctx, data.Email, subdomain)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, "user")
			return
		} else if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "user")
		return
	}

	hasActiveKey, err := cfg.AuthClient.PwdResetHasActiveKey(ctx, usr.ID)
	if err != nil {
		if !errs.IsNotFound(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "password reset")
		return
	}

	if hasActiveKey {
		apicommon.Resp204(c)
		return
	}

	resp, err := cfg.AuthClient.PwdResetCreate(ctx, authmodel.PwdResetCreateRequest{
		UsrID: usr.ID,
	})
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "password reset")
		return
	}

	// Step 3: Send activation email
	err = core.PwdResetEmail(
		ctx,
		url.QueryEscape(resp.ResetKey),
		fmt.Sprintf("%d", resp.ID),
		data.Email,
		usr.NameFirst,
		subdomain,
	)
	if err != nil {
		log.Printf("Error sending activation email: %v", err)
		// Don't fail the signup if email fails, just log it.
	}

	apicommon.Resp204(c)
}

func AuthPwdResetUpdate(c *gin.Context) {
	var data model.PasswordResetUpdateRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	ctx := c.Request.Context()
	cfg.AuthClient.PwdCheckStrength(ctx, authmodel.PwdStrengthRequest{
		Password: data.NewPassword,
	})

	resp, err := cfg.AuthClient.PwdCheckStrength(ctx, authmodel.PwdStrengthRequest{
		Password: data.NewPassword,
	})
	if err != nil {
		apicommon.RespISE(c, "password strength check failed")
		return
	}

	if !resp.Strong {
		apicommon.RespBadRequest(c, "pwd-!strong")
		return
	}

	accUsr, err := cfg.AuthClient.PwdResetKeyGet(ctx, authmodel.PwdResetKeyGetRequest{
		ResetKey: data.ResetKey,
		ID:       data.ResetID,
	})
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "password reset")
		return

	}
	subdomain := utils.SubdomainGet(c.Request.Host)
	acc, err := cfg.AccmgtClient.AccGetByShortName(ctx, subdomain)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "account")
		return
	}

	if acc.ID != accUsr.AccID {
		apicommon.RespNotFound(c, "reset-key-acc")
		return
	}

	_, err = cfg.AuthClient.PwdResetUpdate(ctx, authmodel.PwdResetUpdateRequest{
		ResetKey: data.ResetKey,
		ID:       data.ResetID,
		Pwd:      data.NewPassword,
	})
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "password reset")
		return
	}

	usr, err := cfg.AccmgtClient.UserGetByID(ctx, accUsr.UsrID)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		if errs.IsBadRequest(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "user")
		return
	}

	err = core.PwdResetSuccessMail(ctx, usr.Email, usr.NameFirst, subdomain)
	if err != nil {
		log.Printf("Error sending password reset success email: %v", err)
		// Don't fail the request if email fails, just log it.
	}

	apicommon.Resp204(c)
}

func AuthPwdResetGetReq(c *gin.Context) {
	resetKey := c.Query("reset-key")
	if strings.TrimSpace(resetKey) == "" {
		apicommon.RespBadRequest(c, "reset-key")
		return
	}

	resetIDStr := c.Query("id")
	if strings.TrimSpace(resetIDStr) == "" {
		apicommon.RespBadRequest(c, "id")
		return
	}

	resetID, err := utils.AsUint64(resetIDStr)
	if err != nil {
		apicommon.RespBadRequest(c, "id")
		return
	}

	ctx := c.Request.Context()
	cfg.AuthClient.PwdResetKeyGet(ctx, authmodel.PwdResetKeyGetRequest{
		ResetKey: resetKey,
		ID:       resetID,
	})
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespOk(c, apitypes.BoolTypeNew(false))
			return
		}
		apicommon.RespISE(c, "password reset")
		return
	}

	apicommon.RespOk(c, apitypes.BoolTypeNew(true))
}

func AuthTokenRenew(c *gin.Context) {
	var data authmodel.RefreshTokenRequest
	if err := c.ShouldBindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	ctx := c.Request.Context()
	subdomain := utils.SubdomainGet(c.Request.Host)
	acc, err := cfg.AccmgtClient.AccGetByShortName(ctx, subdomain)
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "account")
		return
	}

	accUsr, err := cfg.AuthClient.RTokenFetchAccUsr(ctx, authmodel.RTokenFetchAccUsrReq{
		RToken: data.RefreshToken,
	})
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "account")
		return
	}

	if acc.ID != accUsr.AccID {
		apicommon.RespUnauthorized(c, "account")
		return
	}

	if acc.AccStateID != &accmgtmodel.AccStateDeleted {
		apicommon.RespNotFound(c, "account")
		return
	}

	usr, err := cfg.AccmgtClient.UserGetByID(ctx, accUsr.UsrID)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "user")
		return
	}

	if usr.UserStateID != accmgtmodel.UserStateActive && usr.UserStateID != accmgtmodel.UserStateNotValidated {
		apicommon.RespBadRequest(c, "user")
		return
	}

	tkn, err := cfg.AuthClient.RenewRefreshToken(ctx, authmodel.RefreshTokenRequest{
		RefreshToken: data.RefreshToken,
	})
	if err != nil {
		if errs.IsNotFound(err) {
			apicommon.RespNotFound(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "token")
		return
	}

	apicommon.RespOk(c, tkn)
}

func AuthTokenRevoke(c *gin.Context) {
	var data authmodel.RTokenRevokeReq
	if err := c.ShouldBindJSON(&data); err != nil {
		apicommon.RespBadRequest(c, "payload")
		return
	}

	ctx := c.Request.Context()
	affRows, err := cfg.AuthClient.RevokeRefreshToken(ctx, data)
	if err != nil {
		if errs.IsBadInput(err) {
			apicommon.RespBadRequest(c, errs.GetReason(err))
			return
		}
		apicommon.RespISE(c, "token")
		return
	}

	apicommon.RespOk(c, affRows)
}
