package main

import (
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/apicommon"
	"github.com/homewizeAI/apicommongin"
	"github.com/homewizeAI/webexpms/api"
)

func main() {
	// Create Gin router
	r := apicommongin.NewRouter()

	// Auth routes group
	authGroup := r.Group("/auth")
	{
		authGroup.POST("/pwd-validate", api.AuthPwdValidate)
		// POST /auth/pwd-validate
		authGroup.GET("/pwd-reset", api.AuthPwdResetGetReq)
		authGroup.POST("/pwd-reset", api.AuthPwdResetReq)
		authGroup.PUT("/pwd-reset", api.AuthPwdResetUpdate)

		authGroup.PUT("/toekn/renew", api.AuthTokenRenew)
		authGroup.DELETE("/token/revoke", api.AuthTokenRevoke)
	}

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.<PERSON>(200, gin.H{
			"status":  "healthy",
			"service": "webexpms",
		})
	})

	// Start server
	port := apicommon.CliPort(3003) // Default port is 3003 (different from authms:3001, accmgtms:3000, onboardms:3002)
	portStr := fmt.Sprintf(":%d", port)
	log.Printf("Starting webexpms server on %s", portStr)
	if err := r.Run(portStr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
