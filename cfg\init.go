package cfg

import (
	"log"

	accmgtclient "github.com/homewizeAI/accmgtms/client"
	authclient "github.com/homewizeAI/authms/client"
	msgclient "github.com/homewizeAI/msgms/client"
	"github.com/pelletier/go-toml"
)

var (
	EnvDomain string
)

var (
	AuthClient   = authclient.NewWithURLPfx("http://localhost:3001")
	AccmgtClient = accmgtclient.NewWithURLPfx("http://localhost:3000")
	MsgClient    = msgclient.NewWithURLPfx("http://localhost:3002")
)

func init() {
	conf, err := toml.LoadFile("conf/env.toml")
	if err != nil {
		log.Printf("Warning: Failed to load config file conf/env.toml: %v", err)
		return
	}

	EnvDomain = conf.Get("env.domain").(string)
}
