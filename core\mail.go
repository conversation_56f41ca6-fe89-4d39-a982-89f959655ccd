package core

import (
	"context"
	"fmt"
	"html/template"
	"log"
	"os"
	"strings"

	"github.com/homewizeAI/msgms/model"
	"github.com/homewizeAI/webexpms/cfg"
)

const (
	mailpwdResetUrl            = "%s/password-reset?key=%s&id=%s"
	mailPwdResetSubject        = "Reset your Password for %s"
	mailPwdResetSuccessSubject = "Your Password has been reset for %s"
	mailSender                 = "<EMAIL>"
)

func PwdResetSuccessMail(ctx context.Context, userEmail, userName, comanyName string) error {

	// Load HTML template from file
	htmlContent, err := loadHTMLTemplateFromFile("../memstorejob/data/templates/html/account_activation.html")
	if err != nil {
		log.Printf("Error loading HTML template from file: %v", err)
		return fmt.Errorf("failed to load HTML template: %w", err)
	}

	// Step 3: Parse HTML content with data
	templateData := map[string]interface{}{
		"customer_name": userName,
	}

	parsedHTML, err := parseHTMLWithData(htmlContent, templateData)
	if err != nil {
		log.Printf("Error parsing HTML template: %v", err)
		return fmt.Errorf("failed to parse HTML template: %w", err)
	}

	// Step 4: Send email via msgms LogEmailCreate API
	emailReq := model.EmailRequest{
		To:      []string{userEmail},
		From:    mailSender,
		Subject: fmt.Sprintf(mailPwdResetSuccessSubject, comanyName),
		Body:    parsedHTML,
		IsHTML:  true,
	}

	_, err = cfg.MsgClient.LogEmailCreate(ctx, emailReq)
	if err != nil {
		return fmt.Errorf("failed to send activation email: %w", err)
	}

	return nil

}

// SendAccountActivationEmail sends account activation email using msgms LogEmailCreate API
func PwdResetEmail(ctx context.Context, resetKey, resetID, userEmail, userName, companyName string) error {
	// Step 1: Construct activation link
	pwdResetLink := constructPwdResetLink(resetKey, resetID)

	// Step 2: Get HTML template from memstore (temporarily using hardcoded template for testing)
	// htmlContent, err := getHTMLTemplateFromMemstore(ctx, rediskeys.HTMLAccActivationTmplName)
	// if err != nil {
	// 	log.Printf("Error getting HTML template: %v", err)
	// 	return fmt.Errorf("failed to get HTML template: %w", err)
	// }

	// Load HTML template from file
	htmlContent, err := loadHTMLTemplateFromFile("../memstorejob/data/templates/html/account_activation.html")
	if err != nil {
		log.Printf("Error loading HTML template from file: %v", err)
		return fmt.Errorf("failed to load HTML template: %w", err)
	}

	// Step 3: Parse HTML content with data
	templateData := map[string]interface{}{
		"customer_name": userName,
		"pwdReset_link": pwdResetLink,
	}

	parsedHTML, err := parseHTMLWithData(htmlContent, templateData)
	if err != nil {
		log.Printf("Error parsing HTML template: %v", err)
		return fmt.Errorf("failed to parse HTML template: %w", err)
	}

	// Step 4: Send email via msgms LogEmailCreate API
	emailReq := model.EmailRequest{
		To:      []string{userEmail},
		From:    mailSender,
		Subject: fmt.Sprintf(mailPwdResetSubject, companyName),
		Body:    parsedHTML,
		IsHTML:  true,
	}

	_, err = cfg.MsgClient.LogEmailCreate(ctx, emailReq)
	if err != nil {
		return fmt.Errorf("failed to send activation email: %w", err)
	}

	return nil
}

// constructActivationLink builds the activation URL using EnvDomain from configuration
func constructPwdResetLink(resetKey, resetID string) string {
	// Use EnvDomain from configuration which already includes protocol
	domain := strings.TrimSuffix(cfg.EnvDomain, "/")

	// Construct the activation link using the constant template
	return fmt.Sprintf(mailpwdResetUrl, domain, resetKey, resetID)
}

// getHTMLTemplateFromMemstore retrieves HTML template from memstore using redisclient
// Temporarily commented out for testing without Redis
// func getHTMLTemplateFromMemstore(ctx context.Context, templateKey string) (string, error) {
// 	redisClient := memstore.GetClient()
//
// 	htmlContent, err := redisClient.Get(ctx, templateKey).Result()
// 	if err != nil {
// 		return "", fmt.Errorf("failed to get template %s from memstore: %w", templateKey, err)
// 	}
//
// 	return htmlContent, nil
// }

// parseHTMLWithData parses HTML template with the provided data
func parseHTMLWithData(htmlContent string, data map[string]interface{}) (string, error) {
	tmpl, err := template.New("email").Parse(htmlContent)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	var buf strings.Builder
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buf.String(), nil
}

// loadHTMLTemplateFromFile loads HTML template content from a file
func loadHTMLTemplateFromFile(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read template file %s: %w", filePath, err)
	}
	return string(content), nil
}
