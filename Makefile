# Makefile for webexpms project

.PHONY: help build test test-unit test-integration test-all clean run dev deps lint fmt vet coverage

# Default target
help:
	@echo "Available targets:"
	@echo "  build              - Build the webexpms binary"
	@echo "  test               - Run all tests (unit + integration)"
	@echo "  test-unit          - Run unit tests only"
	@echo "  test-integration   - Run integration tests only"
	@echo "  test-all           - Run all tests with verbose output"
	@echo "  clean              - Clean up build artifacts"
	@echo "  run                - Build and run the application"
	@echo "  dev                - Run the application in development mode"
	@echo "  deps               - Download and tidy dependencies"
	@echo "  lint               - Run golangci-lint"
	@echo "  fmt                - Format code with gofmt"
	@echo "  vet                - Run go vet"
	@echo "  coverage           - Run tests with coverage report"

# Build the application
build:
	@echo "Building webexpms..."
	go build -o webexpms .

# Build for Windows
build-windows:
	@echo "Building webexpms for Windows..."
	GOOS=windows GOARCH=amd64 go build -o webexpms.exe .

# Run all tests
test:
	@echo "Running all tests..."
	go test ./api ./model -v
	go test . -v

# Run unit tests only
test-unit:
	@echo "Running unit tests..."
	go test ./api ./model -v

# Run integration tests only
test-integration:
	@echo "Running integration tests..."
	go test . -v

# Run all tests with verbose output and race detection
test-all:
	@echo "Running all tests with race detection..."
	go test -race -v ./api ./model .

# Run tests with coverage
coverage:
	@echo "Running tests with coverage..."
	go test -coverprofile=coverage.out ./api ./model .
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Clean up build artifacts
clean:
	@echo "Cleaning up..."
	rm -f webexpms webexpms.exe
	rm -f coverage.out coverage.html

# Build and run the application
run: build
	@echo "Starting webexpms..."
	./webexpms

# Run the application in development mode (with auto-restart on file changes)
dev:
	@echo "Starting webexpms in development mode..."
	@if command -v air > /dev/null; then \
		air; \
	else \
		echo "Air not found. Install with: go install github.com/cosmtrek/air@latest"; \
		echo "Running without auto-restart..."; \
		go run .; \
	fi

# Download and tidy dependencies
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Run go vet
vet:
	@echo "Running go vet..."
	go vet ./...

# Run golangci-lint (if available)
lint:
	@echo "Running linter..."
	@if command -v golangci-lint > /dev/null; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not found. Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
		echo "Running go vet instead..."; \
		go vet ./...; \
	fi

# Install development tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Run quality checks
check: fmt vet lint test

# Docker targets
docker-build:
	@echo "Building Docker image..."
	docker build -t webexpms .

docker-run:
	@echo "Running Docker container..."
	docker run -p 3003:3003 webexpms

# Benchmark tests
benchmark:
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./...

# Generate documentation
docs:
	@echo "Generating documentation..."
	@if command -v godoc > /dev/null; then \
		echo "Starting godoc server on http://localhost:6060"; \
		godoc -http=:6060; \
	else \
		echo "godoc not found. Install with: go install golang.org/x/tools/cmd/godoc@latest"; \
	fi

# Security scan
security:
	@echo "Running security scan..."
	@if command -v gosec > /dev/null; then \
		gosec ./...; \
	else \
		echo "gosec not found. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Show project statistics
stats:
	@echo "Project Statistics:"
	@echo "=================="
	@echo "Go files:"
	@find . -name "*.go" -not -path "./vendor/*" | wc -l
	@echo "Lines of code:"
	@find . -name "*.go" -not -path "./vendor/*" -exec cat {} \; | wc -l
	@echo "Test files:"
	@find . -name "*_test.go" -not -path "./vendor/*" | wc -l
	@echo "Dependencies:"
	@go list -m all | wc -l
