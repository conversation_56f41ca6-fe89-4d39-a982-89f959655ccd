# WebExpMS - Web Experience Management Service

WebExpMS is a microservice that provides authentication-related endpoints for web experience management, including password validation, reset, and change functionality.

## Features

- **Password Validation**: Comprehensive password strength validation with detailed feedback
- **Password Reset**: Secure password reset functionality via email
- **Password Change**: Authenticated password change for existing users
- **Password Strength Check**: Simple endpoint to check password strength

## API Endpoints

### Auth Routes (`/auth`)

- `POST /auth/pwd-validate` - Validate password strength with detailed feedback
- `POST /auth/pwd-reset` - Initiate password reset process
- `POST /auth/pwd-reset-confirm` - Confirm password reset with token
- `POST /auth/pwd-change` - Change password for authenticated users
- `POST /auth/pwd-strength` - Simple password strength check

### Health Check

- `GET /health` - Service health check endpoint

## Project Structure

```
webexpms/
├── api/                    # API handlers and validation logic
│   ├── auth.go            # Authentication-related endpoints
│   └── validation.go      # Password validation utilities
├── cfg/                   # Configuration and client initialization
│   └── init.go           # Service clients and environment setup
├── conf/                  # Configuration files
│   └── env.toml          # Environment configuration
├── core/                  # Core business logic (if needed)
├── jwt/                   # JWT utilities (if needed)
├── model/                 # Data models and request/response structures
│   └── auth.go           # Authentication-related models
├── go.mod                # Go module definition
├── main.go               # Application entry point
└── README.md             # This file
```

## Dependencies

- **Gin**: HTTP web framework
- **AuthMS Client**: Client for authentication microservice
- **API Common**: Common API utilities and response helpers
- **TOML**: Configuration file parsing

## Configuration

The service uses a TOML configuration file located at `conf/env.toml`:

```toml
[env]
domain = "localhost"
```

## Running the Service

1. Ensure all dependencies are installed:
   ```bash
   go mod tidy
   ```

2. Start the service:
   ```bash
   go run main.go
   ```

3. The service will start on port 3003 by default (configurable via command line)

## Password Validation Rules

The service implements comprehensive password validation:

- **Minimum length**: 8 characters
- **Maximum length**: 128 characters
- **Required characters**:
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one digit
- **Recommended**: At least one special character
- **Security checks**:
  - No common weak patterns
  - No sequential characters
  - No dictionary words

## Integration

This service integrates with:

- **AuthMS**: For core authentication operations
- **API Common**: For standardized API responses
- **Message Service**: For email notifications (password reset)

## Development

To add new endpoints:

1. Define request/response models in `model/`
2. Add validation logic in `api/validation.go`
3. Implement handlers in `api/auth.go`
4. Register routes in `main.go`

## Testing

Run tests with:
```bash
go test ./...
```

## Port Configuration

- Default port: 3003
- AuthMS: 3001
- AccMgtMS: 3000
- OnboardMS: 3002