package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/homewizeAI/webexpms/api"
	"github.com/homewizeAI/webexpms/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// IntegrationTestSuite defines the test suite for integration tests
type IntegrationTestSuite struct {
	suite.Suite
	router *gin.Engine
	server *httptest.Server
}

// SetupSuite runs before all tests in the suite
func (suite *IntegrationTestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)
	
	// Create router with all routes
	suite.router = gin.New()
	
	// Health endpoint
	suite.router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})
	
	// Auth routes group
	authGroup := suite.router.Group("/auth")
	{
		authGroup.POST("/pwd-validate", api.PasswordValidateHandler)
		authGroup.POST("/pwd-reset", api.PasswordResetHandler)
		authGroup.POST("/pwd-reset-confirm", api.PasswordResetConfirmHandler)
		authGroup.POST("/pwd-change", api.PasswordChangeHandler)
		authGroup.POST("/pwd-strength", api.PasswordStrengthCheckHandler)
	}
	
	// Start test server
	suite.server = httptest.NewServer(suite.router)
}

// TearDownSuite runs after all tests in the suite
func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
}

// TestHealthEndpoint tests the health check endpoint
func (suite *IntegrationTestSuite) TestHealthEndpoint() {
	resp, err := http.Get(suite.server.URL + "/health")
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	suite.NoError(err)
	suite.Equal("ok", response["status"])
}

// TestPasswordValidationWorkflow tests the complete password validation workflow
func (suite *IntegrationTestSuite) TestPasswordValidationWorkflow() {
	testCases := []struct {
		name           string
		password       string
		expectedValid  bool
		expectedStrong bool
	}{
		{
			name:           "strong password",
			password:       "StrongPassword123!",
			expectedValid:  true,
			expectedStrong: true,
		},
		{
			name:           "weak password - no uppercase",
			password:       "weakpassword123!",
			expectedValid:  true,
			expectedStrong: false,
		},
		{
			name:           "invalid password - too short",
			password:       "short",
			expectedValid:  false,
			expectedStrong: false,
		},
	}
	
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			// Test password validation endpoint
			validateReq := model.PasswordValidateRequest{Password: tc.password}
			validateBody, err := json.Marshal(validateReq)
			suite.NoError(err)
			
			resp, err := http.Post(
				suite.server.URL+"/auth/pwd-validate",
				"application/json",
				bytes.NewBuffer(validateBody),
			)
			suite.NoError(err)
			defer resp.Body.Close()
			
			if tc.expectedValid {
				suite.Equal(http.StatusOK, resp.StatusCode)
				
				var validateResp model.PasswordValidateResponse
				err = json.NewDecoder(resp.Body).Decode(&validateResp)
				suite.NoError(err)
				suite.Equal(tc.expectedValid, validateResp.Valid)
				suite.Equal(tc.expectedStrong, validateResp.Strong)
			} else {
				// Invalid passwords should return OK with valid=false
				suite.Equal(http.StatusOK, resp.StatusCode)
			}
			
			// Test password strength endpoint
			strengthBody, err := json.Marshal(validateReq)
			suite.NoError(err)
			
			strengthResp, err := http.Post(
				suite.server.URL+"/auth/pwd-strength",
				"application/json",
				bytes.NewBuffer(strengthBody),
			)
			suite.NoError(err)
			defer strengthResp.Body.Close()
			
			suite.Equal(http.StatusOK, strengthResp.StatusCode)
			
			var strengthResult bool
			err = json.NewDecoder(strengthResp.Body).Decode(&strengthResult)
			suite.NoError(err)
			
			// Strength endpoint should return true only for valid AND strong passwords
			expectedStrengthResult := tc.expectedValid && tc.expectedStrong
			suite.Equal(expectedStrengthResult, strengthResult)
		})
	}
}

// TestPasswordResetWorkflow tests the complete password reset workflow
func (suite *IntegrationTestSuite) TestPasswordResetWorkflow() {
	// Step 1: Request password reset
	resetReq := model.PasswordResetRequest{Email: "<EMAIL>"}
	resetBody, err := json.Marshal(resetReq)
	suite.NoError(err)
	
	resp, err := http.Post(
		suite.server.URL+"/auth/pwd-reset",
		"application/json",
		bytes.NewBuffer(resetBody),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var resetResp model.PasswordResetResponse
	err = json.NewDecoder(resp.Body).Decode(&resetResp)
	suite.NoError(err)
	suite.True(resetResp.Success)
	suite.Contains(resetResp.Message, "successfully")
	
	// Step 2: Confirm password reset with new password
	confirmReq := model.PasswordResetConfirmRequest{
		Token:       "test-token-123",
		NewPassword: "NewStrongPassword123!",
	}
	confirmBody, err := json.Marshal(confirmReq)
	suite.NoError(err)
	
	confirmResp, err := http.Post(
		suite.server.URL+"/auth/pwd-reset-confirm",
		"application/json",
		bytes.NewBuffer(confirmBody),
	)
	suite.NoError(err)
	defer confirmResp.Body.Close()
	
	suite.Equal(http.StatusOK, confirmResp.StatusCode)
	
	var confirmRespData model.PasswordResetConfirmResponse
	err = json.NewDecoder(confirmResp.Body).Decode(&confirmRespData)
	suite.NoError(err)
	suite.True(confirmRespData.Success)
}

// TestPasswordChangeWorkflow tests the password change workflow
func (suite *IntegrationTestSuite) TestPasswordChangeWorkflow() {
	changeReq := model.PasswordChangeRequest{
		CurrentPassword: "OldPassword123!",
		NewPassword:     "NewStrongPassword123!",
	}
	changeBody, err := json.Marshal(changeReq)
	suite.NoError(err)
	
	resp, err := http.Post(
		suite.server.URL+"/auth/pwd-change",
		"application/json",
		bytes.NewBuffer(changeBody),
	)
	suite.NoError(err)
	defer resp.Body.Close()
	
	suite.Equal(http.StatusOK, resp.StatusCode)
	
	var changeResp model.PasswordChangeResponse
	err = json.NewDecoder(resp.Body).Decode(&changeResp)
	suite.NoError(err)
	suite.True(changeResp.Success)
	suite.Contains(changeResp.Message, "successfully")
}

// TestErrorHandling tests various error scenarios
func (suite *IntegrationTestSuite) TestErrorHandling() {
	testCases := []struct {
		name           string
		endpoint       string
		requestBody    string
		expectedStatus int
	}{
		{
			name:           "invalid JSON for password validation",
			endpoint:       "/auth/pwd-validate",
			requestBody:    `{"invalid": json}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty password validation",
			endpoint:       "/auth/pwd-validate",
			requestBody:    `{"password": ""}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid email for password reset",
			endpoint:       "/auth/pwd-reset",
			requestBody:    `{"email": "invalid-email"}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "weak password for reset confirm",
			endpoint:       "/auth/pwd-reset-confirm",
			requestBody:    `{"token": "test", "new_password": "weak"}`,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "weak password for change",
			endpoint:       "/auth/pwd-change",
			requestBody:    `{"current_password": "old", "new_password": "weak"}`,
			expectedStatus: http.StatusBadRequest,
		},
	}
	
	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			resp, err := http.Post(
				suite.server.URL+tc.endpoint,
				"application/json",
				bytes.NewBufferString(tc.requestBody),
			)
			suite.NoError(err)
			defer resp.Body.Close()
			
			suite.Equal(tc.expectedStatus, resp.StatusCode)
		})
	}
}

// TestConcurrentRequests tests handling of concurrent requests
func (suite *IntegrationTestSuite) TestConcurrentRequests() {
	const numRequests = 10
	
	// Create channels for synchronization
	done := make(chan bool, numRequests)
	errors := make(chan error, numRequests)
	
	// Make concurrent requests
	for i := 0; i < numRequests; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			req := model.PasswordValidateRequest{
				Password: fmt.Sprintf("TestPassword%d!", id),
			}
			body, err := json.Marshal(req)
			if err != nil {
				errors <- err
				return
			}
			
			resp, err := http.Post(
				suite.server.URL+"/auth/pwd-validate",
				"application/json",
				bytes.NewBuffer(body),
			)
			if err != nil {
				errors <- err
				return
			}
			defer resp.Body.Close()
			
			if resp.StatusCode != http.StatusOK {
				errors <- fmt.Errorf("unexpected status code: %d", resp.StatusCode)
				return
			}
		}(i)
	}
	
	// Wait for all requests to complete
	timeout := time.After(5 * time.Second)
	completed := 0
	
	for completed < numRequests {
		select {
		case <-done:
			completed++
		case err := <-errors:
			suite.Fail("Concurrent request failed", err.Error())
		case <-timeout:
			suite.Fail("Timeout waiting for concurrent requests")
		}
	}
	
	// Check if there are any remaining errors
	select {
	case err := <-errors:
		suite.Fail("Concurrent request failed", err.Error())
	default:
		// No errors, test passed
	}
}

// TestIntegrationSuite runs the integration test suite
func TestIntegrationSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}
